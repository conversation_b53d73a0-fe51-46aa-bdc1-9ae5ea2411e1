import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

export class BlockUserByPageIdDto {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the user to block',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}

export class UnblockUserByPageIdDto {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the user to unblock',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'userId must be a valid UUID v4' })
  userId!: string;
}
