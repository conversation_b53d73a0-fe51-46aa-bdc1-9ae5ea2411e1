import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItem, UserListItemWithPageRole } from 'src/models';

export class SearchModeratorsForPageCreationReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of users available for page creation',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of users available for page creation',
    example: 15,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchModeratorsToAddByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of users available to be added as moderators',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of users available to be added as moderators',
    example: 10,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchPageAdminsByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItemWithPageRole),
    },
    description: 'List of page administrators',
    example: [],
  })
  users!: UserListItemWithPageRole[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of page administrators',
    example: 3,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchPageMembersByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of page members',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of page members',
    example: 20,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchUsersToAddByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of users available to be added as page members',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of users available to be added as page members',
    example: 10,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchAdministrationByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItemWithPageRole),
    },
    description: 'List of page administrators and moderators',
    example: [],
  })
  users!: UserListItemWithPageRole[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of page administrators and moderators',
    example: 3,
    minimum: 0,
  })
  userCount!: number;
}
