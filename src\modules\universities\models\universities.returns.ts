import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UniversityItem } from 'src/models';

export class SearchUniversitiesReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(UniversityItem) },
    description: 'List of universities',
    example: [],
  })
  universities!: UniversityItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of universities',
    example: 0,
    minimum: 0,
  })
  universityCount!: number;
}
