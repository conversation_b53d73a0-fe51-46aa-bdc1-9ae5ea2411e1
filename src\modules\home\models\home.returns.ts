import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { MarkerItem } from 'src/models';
import { BasicAccountListItem } from 'src/models/basic-account-list-item';
import { IventCardItem } from 'src/models/ivent-card-item';

export class FeedReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(IventCardItem),
    },
    description: 'List of ivents',
    example: [],
  })
  ivents!: IventCardItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of ivents',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class MapReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(MarkerItem),
    },
    description: 'List of ivent markers',
    example: [],
  })
  ivents!: MarkerItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of ivent markers',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class SearchAccountReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(BasicAccountListItem),
    },
    description: 'List of accounts',
    example: [],
  })
  accounts!: BasicAccountListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of accounts',
    example: 0,
    minimum: 0,
  })
  accountCount!: number;
}

export class SearchIventReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(IventCardItem),
    },
    description: 'List of ivents',
    example: [],
  })
  ivents!: IventCardItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of ivents',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}
