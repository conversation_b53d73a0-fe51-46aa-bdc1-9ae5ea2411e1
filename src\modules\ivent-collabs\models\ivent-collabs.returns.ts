import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { CollabratorListItem } from 'src/models';
import { BasicAccountListItem } from 'src/models/basic-account-list-item';

export class SearchCollabsForIventCreationReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(BasicAccountListItem),
    },
    description: 'List of accounts available for collaboration during ivent creation',
    example: [],
  })
  accounts!: BasicAccountListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of accounts available for collaboration',
    example: 12,
    minimum: 0,
  })
  accountCount!: number;
}

export class SearchCollabsReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(CollabratorListItem),
    },
    description: 'List of collaborators with their membership and friendship status',
    example: [],
  })
  collabs!: CollabratorListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of collaborators found',
    example: 8,
    minimum: 0,
  })
  collabCount!: number;
}
