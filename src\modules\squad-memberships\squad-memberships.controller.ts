import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { FriendListingTypeEnum } from 'src/constants/enums';
import { ApiResponseObject } from 'src/models/api-response-object';
import { EmptyReturn } from 'src/models/empty-return';
import { InviteFriendsByIventIdDto, JoinIventAndCreateSquadByIventIdDto } from './models/squad-memberships.dto';
import {
  SearchInvitableUsersByIventIdReturn,
  SearchParticipantsByIventIdReturn,
} from './models/squad-memberships.returns';
import { SquadMembershipsService } from './squad-memberships.service';

@ApiTags('squadMemberships')
@Controller('squadMemberships')
export class SquadMembershipsController {
  constructor(private readonly squadMembershipsService: SquadMembershipsService) {}

  @ApiOperation({
    summary: 'Ivente katılırken davet edilebilecek hesapları listeler',
  })
  @ApiResponseObject({
    model: SearchInvitableUsersByIventIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get(':iventId/search')
  async searchInvitableUsersByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
    @Query('type') type: FriendListingTypeEnum,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.squadMembershipsService.searchInvitableUsersByIventId({
      sessionId,
      sessionRole,
      iventId,
      type,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary:
      'Ivent IDsi ile (eğer iventte yetkiliyse): ivente katılan bütün hesapları, (eğer ivente katılmayan bir user ise): ivente katılan ya da favorilemiş arkadaşları, (eğer ivente katılan bir user ise): ivente birlikte katıldığı arkadaşları listeler',
  })
  @ApiResponseObject({
    model: SearchParticipantsByIventIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get(':iventId')
  async searchParticipantsByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.squadMembershipsService.searchParticipantsByIventId({
      sessionId,
      sessionRole,
      iventId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile seçilen hesaplarla birlikte ivente katılır (seçili hesaplara davet gönderilir)',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':iventId/join')
  async joinIventAndCreateSquadByIventId(
    @Body()
    joinIventAndCreateSquadByIventIdDto: JoinIventAndCreateSquadByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.squadMembershipsService.joinIventAndCreateSquadByIventId({
      sessionId,
      sessionRole,
      iventId,
      ...joinIventAndCreateSquadByIventIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile etkinlikten ayrılınır',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':iventId/leave')
  async leaveSquadByIventId(
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.squadMembershipsService.leaveSquadByIventId({
      sessionId,
      sessionRole,
      iventId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Ivent IDsi ile seçilen hesaplar davet edilir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':iventId/invite')
  async inviteFriendsByIventId(
    @Body()
    inviteFriendsByIventIdDto: InviteFriendsByIventIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('iventId', new ParseUUIDPipe({ version: '4' })) iventId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.squadMembershipsService.inviteFriendsByIventId({
      sessionId,
      sessionRole,
      iventId,
      ...inviteFriendsByIventIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
