import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItem } from 'src/models/user-list-item';

export class SearchPageBlocklistByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of users who are blocked by this page',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of blocked users',
    example: 0,
    minimum: 0,
  })
  userCount!: number;
}
