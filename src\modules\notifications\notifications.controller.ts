import {
  Controller,
  DefaultV<PERSON>uePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { NotificationReplyTypeEnum } from 'src/constants/enums';
import { ApiResponseObject } from 'src/models/api-response-object';
import { EmptyReturn } from 'src/models/empty-return';
import { GetNotificationsReturn } from './models/notifications.returns';
import { NotificationsService } from './notifications.service';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @ApiOperation({
    summary: 'Bildirimleri listeler',
  })
  @ApiResponseObject({
    model: GetNotificationsReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('')
  async getNotifications(
    @Res({ passthrough: true }) res: Response,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.notificationsService.getNotifications({
      sessionId,
      sessionRole,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Squad IDsi ile seçilen davete "accept" ya da "reject" yanıtı verilir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':id/reply')
  async replyInvitationBySquadId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) notificationId: string,
    @Query('reply') replyType: NotificationReplyTypeEnum,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.notificationsService.replyInvitationByNotificationId({
      sessionId,
      sessionRole,
      notificationId,
      replyType,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
