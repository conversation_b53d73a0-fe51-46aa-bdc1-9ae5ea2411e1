import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, PageMembershipStatusEnum, UserRelationshipStatusEnum } from 'src/entities';

export class CollabratorListItem {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the collaborator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  collabId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the collaborator',
    example: 'Photography Club Istanbul or john_doe',
  })
  collabName!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(AccountTypeEnum),
    description: 'Type of the collaborator account (user or page)',
    example: AccountTypeEnum.USER,
  })
  collabType!: AccountTypeEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the collaborator image',
    example: 'https://example.com/collaborator-image.jpg',
    format: 'url',
  })
  collabImageUrl!: string | null;

  @ApiProperty({
    type: 'string',
    enum: Object.values(PageMembershipStatusEnum),
    description: 'Membership status of the collaborator in the page',
    example: PageMembershipStatusEnum.ADMIN,
  })
  pageMembershipStatus!: PageMembershipStatusEnum;

  @ApiProperty({
    type: 'string',
    enum: Object.values(UserRelationshipStatusEnum),
    description: 'Relationship status of the collaborator with the current user',
    example: UserRelationshipStatusEnum.ACCEPTED,
  })
  relationshipStatus!: UserRelationshipStatusEnum;
}
