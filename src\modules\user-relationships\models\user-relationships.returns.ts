import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { GroupListItem } from 'src/models';
import { UserListItem } from 'src/models/user-list-item';

export class GetUserBlocklistReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of users who are blocked by this user',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of blocked users',
    example: 0,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchFriendsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(GroupListItem),
    },
    description: 'List of groups',
    example: [],
  })
  groups!: GroupListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of groups',
    example: 0,
    minimum: 0,
  })
  groupCount!: number;

  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of friends',
    example: [],
  })
  friends!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of friends',
    example: 0,
    minimum: 0,
  })
  friendCount!: number;
}
